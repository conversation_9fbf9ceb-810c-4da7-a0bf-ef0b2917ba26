import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/theme_provider.dart';
import '../../core/services/theme_service.dart' as theme_service;

class ThemeDemoScreen extends StatelessWidget {
  const ThemeDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isDark = themeProvider.isDarkMode(context);
        
        return Scaffold(
          backgroundColor: AppColors.getBackground(isDark),
          appBar: AppBar(
            title: const Text('Dark Mode Demo'),
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.textWhite,
          ),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current Theme Info
                Card(
                  color: AppColors.getCardBackground(isDark),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Theme',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.getTextPrimary(isDark),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Mode: ${themeProvider.currentThemeDisplayName}',
                          style: TextStyle(
                            color: AppColors.getTextSecondary(isDark),
                          ),
                        ),
                        Text(
                          'Is Dark: ${isDark ? 'Yes' : 'No'}',
                          style: TextStyle(
                            color: AppColors.getTextSecondary(isDark),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Theme Controls
                Text(
                  'Theme Controls',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.getTextPrimary(isDark),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Light Theme Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      themeProvider.setThemeMode(theme_service.ThemeMode.light);
                    },
                    icon: const Icon(Icons.light_mode),
                    label: const Text('Light Theme'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.textWhite,
                    ),
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // Dark Theme Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      themeProvider.setThemeMode(theme_service.ThemeMode.dark);
                    },
                    icon: const Icon(Icons.dark_mode),
                    label: const Text('Dark Theme'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.textWhite,
                    ),
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // System Theme Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      themeProvider.setThemeMode(theme_service.ThemeMode.system);
                    },
                    icon: const Icon(Icons.brightness_auto),
                    label: const Text('System Theme'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.textWhite,
                    ),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Color Samples
                Text(
                  'Color Samples',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.getTextPrimary(isDark),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Color Grid
                Expanded(
                  child: GridView.count(
                    crossAxisCount: 2,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                    children: [
                      _buildColorSample(
                        'Background',
                        AppColors.getBackground(isDark),
                        isDark,
                      ),
                      _buildColorSample(
                        'Surface',
                        AppColors.getSurface(isDark),
                        isDark,
                      ),
                      _buildColorSample(
                        'Card Background',
                        AppColors.getCardBackground(isDark),
                        isDark,
                      ),
                      _buildColorSample(
                        'Primary',
                        AppColors.primary,
                        isDark,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildColorSample(String name, Color color, bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.getBorder(isDark),
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          name,
          style: TextStyle(
            color: _getContrastColor(color),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Color _getContrastColor(Color color) {
    // Simple contrast calculation
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}
